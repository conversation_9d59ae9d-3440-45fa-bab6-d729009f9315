from datasets import load_dataset
import json

# 加载数据集
print("正在下载数据集...")
ds = load_dataset("cais/mmlu", "college_mathematics")

# 保存数据集
print("正在保存数据集...")
with open("mmlu_college_mathematics.json", "w", encoding="utf-8") as f:
    for split in ds.keys():
        print(f"处理 {split} 分割...")
        for item in ds[split]:
            f.write(json.dumps(item, ensure_ascii=False) + "\n")

print("数据集下载和保存完成！")
