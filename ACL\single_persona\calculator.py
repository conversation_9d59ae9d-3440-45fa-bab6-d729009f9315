import json
import itertools
from typing import Dict, List, Tuple, Set

def load_persona_data(file_path: str) -> Dict:
    """加载persona数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def extract_all_personas(data: Dict) -> List[Tuple[str, str, Set[int]]]:
    """提取所有persona及其答对的题目"""
    personas = []
    
    for category, category_data in data.items():
        for persona_name, persona_info in category_data.items():
            correct_questions = set(persona_info['correct_questions'])
            personas.append((category, persona_name, correct_questions))
    
    return personas

def find_optimal_three_personas(personas: List[Tuple[str, str, Set[int]]]) -> Tuple[List[Tuple[str, str]], int, Set[int]]:
    """找出最优的三个persona组合"""
    max_coverage = 0
    best_combination = None
    best_union = set()
    
    # 遍历所有可能的三个persona组合
    for combination in itertools.combinations(personas, 3):
        # 计算三个persona答对题目的并集
        union_questions = set()
        for _, _, correct_questions in combination:
            union_questions.update(correct_questions)
        
        coverage = len(union_questions)
        
        if coverage > max_coverage:
            max_coverage = coverage
            best_combination = [(cat, name) for cat, name, _ in combination]
            best_union = union_questions
    
    return best_combination, max_coverage, best_union

def analyze_persona_performance(data: Dict) -> None:
    """分析persona性能并找出最优组合"""
    # 提取所有persona
    personas = extract_all_personas(data)
    
    print(f"总共有 {len(personas)} 个persona")
    print("\n各persona的准确率排名（前10）:")
    
    # 按准确率排序
    sorted_personas = sorted(personas, key=lambda x: len(x[2]), reverse=True)
    for i, (category, name, correct_questions) in enumerate(sorted_personas[:10]):
        accuracy = len(correct_questions) / 1319  # 假设总题目数为500
        print(f"{i+1:2d}. {category:20s} - {name:20s}: {len(correct_questions):3d}/1319 ({accuracy:.3f})")
    
    print("\n" + "="*80)
    print("寻找最优三个persona组合...")
    
    # 找出最优组合
    best_combination, max_coverage, best_union = find_optimal_three_personas(personas)
    
    print(f"\n最优三个persona组合:")
    for i, (category, name) in enumerate(best_combination, 1):
        # 找到对应的准确信息
        for cat, nm, correct_qs in personas:
            if cat == category and nm == name:
                accuracy = len(correct_qs) / 1319
                print(f"{i}. {category} - {name}: {len(correct_qs)}/1319 ({accuracy:.3f})")
                break
    
    print(f"\n组合覆盖的题目数: {max_coverage}/1319 ({max_coverage/1319:.3f})")
    
    # 分析各个persona的贡献
    print(f"\n详细分析:")
    individual_sets = []
    for category, name in best_combination:
        for cat, nm, correct_qs in personas:
            if cat == category and nm == name:
                individual_sets.append((name, correct_qs))
                break
    
    # 计算每个persona独有的贡献
    for i, (name, questions) in enumerate(individual_sets):
        other_questions = set()
        for j, (_, other_qs) in enumerate(individual_sets):
            if i != j:
                other_questions.update(other_qs)
        
        unique_contribution = questions - other_questions
        print(f"{name} 独有贡献: {len(unique_contribution)} 题")
    
    # 计算两两交集
    print(f"\n两两交集分析:")
    for i in range(len(individual_sets)):
        for j in range(i+1, len(individual_sets)):
            name1, qs1 = individual_sets[i]
            name2, qs2 = individual_sets[j]
            intersection = qs1 & qs2
            print(f"{name1} ∩ {name2}: {len(intersection)} 题")
    
    # 三者交集
    if len(individual_sets) == 3:
        three_way_intersection = individual_sets[0][1] & individual_sets[1][1] & individual_sets[2][1]
        print(f"\n三者交集: {len(three_way_intersection)} 题")

def find_top_combinations(personas: List[Tuple[str, str, Set[int]]], top_n: int = 5) -> List[Tuple[List[Tuple[str, str]], int]]:
    """找出前N个最优组合"""
    combinations_coverage = []
    
    for combination in itertools.combinations(personas, 3):
        union_questions = set()
        for _, _, correct_questions in combination:
            union_questions.update(correct_questions)
        
        coverage = len(union_questions)
        combo_info = [(cat, name) for cat, name, _ in combination]
        combinations_coverage.append((combo_info, coverage))
    
    # 按覆盖率排序，返回前N个
    combinations_coverage.sort(key=lambda x: x[1], reverse=True)
    return combinations_coverage[:top_n]

# 主程序
if __name__ == "__main__":
    # 假设数据已经加载到变量data中
    # 这里需要替换为实际的文件路径
    data = load_persona_data('C:\\Users\\<USER>\\Desktop\\paper\\GSM8K\\persona\\all_persona_results.json')

    # 如果数据已经在内存中，可以直接使用
    # 这里假设数据变量名为data
    
    print("Persona组合优化分析")
    print("="*80)
    
    # 执行分析
    analyze_persona_performance(data)
    
    # 如果要查看前5个最优组合
    personas = extract_all_personas(data)
    top_combinations = find_top_combinations(personas, 5)
    # 
    print(f"\n前5个最优组合:")
    for i, (combination, coverage) in enumerate(top_combinations, 1):
        print(f"\n第{i}名 - 覆盖 {coverage}/1319 题 ({coverage/1319:.3f}):")
        for category, name in combination:
            print(f"  - {category} - {name}")

    print("\n程序已准备就绪，请确保数据文件路径正确后运行。")