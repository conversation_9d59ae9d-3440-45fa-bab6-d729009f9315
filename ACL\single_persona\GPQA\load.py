from datasets import load_dataset
import json

# Diamond 难度子集
ds_diamond = load_dataset("idavidrein/gpqa", "gpqa_main")

filtered_data = []
for item in ds_diamond["train"]:
    filtered_item = {
        "Pre-Revision Question": item["Pre-Revision Question"],
        "Pre-Revision Correct Answer": item["Pre-Revision Correct Answer"],
        "Pre-Revision Incorrect Answer 1": item["Pre-Revision Incorrect Answer 1"],
        "Pre-Revision Incorrect Answer 2": item["Pre-Revision Incorrect Answer 2"],
        "Pre-Revision Incorrect Answer 3": item["Pre-Revision Incorrect Answer 3"]
    }
    filtered_data.append(filtered_item)

with open("gpqa_main.jsonl", "w") as f:
    for item in filtered_data:
        f.write(json.dumps(item, ensure_ascii=False) + "\n")
